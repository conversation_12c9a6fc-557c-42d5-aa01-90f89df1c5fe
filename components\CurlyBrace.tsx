// components/CurlyBrace.tsx
import React from 'react';
import Svg, { Path } from 'react-native-svg';
import { StyleProp, ViewStyle } from 'react-native';

interface CurlyProps {
  braceWidth: number;
  braceHeight: number;
  side: 'left' | 'right' | 'top' | 'bottom';
  stroke?: string;
  strokeWidth?: number;
  topRatio?: number;      // 0-1，上半段所占比例，默认 0.5
  style?: StyleProp<ViewStyle>; // 添加 style 属性
}

const genBracePath = (width = 20, height = 100, topRatio = 0.5, strokeWidth = 2) => {
  const H = height;
  const W = width;
  const yT = H * topRatio;              // 上半段终点
  const hAdj = strokeWidth / 2;

  // 两段三次贝塞尔:
  return `
    M${W} ${hAdj}
    C 0 0, ${W} ${yT}, 0 ${yT}
    C ${W} ${yT}, 0 ${H}, ${W} ${H - hAdj}
  `;
};

export const CurlyBrace: React.FC<CurlyProps> = ({
  braceWidth = 20,
  braceHeight = 100,
  side = 'left',
  stroke = 'black',
  strokeWidth = 2,
  topRatio = 0.5,
  style,
}) => {
  const d = genBracePath(braceWidth, braceHeight, topRatio);

  const transforms = {
    left:  '',                                   // {  向右开
    right: `translate(${braceWidth}) scale(-1,1)`,    // }  镜像
    top:   `rotate(90) scale(1,-1)`,             // 向上的 {  横放
    bottom:`translate(0,${braceHeight}) rotate(-90)`, // 向下的 }
  } as const;

  // For top/bottom orientations, we need to swap width and height
  const svgWidth = side === 'top' || side === 'bottom' ? braceHeight : braceWidth;
  const svgHeight = side === 'top' || side === 'bottom' ? braceWidth : braceHeight;
  
  return (
    <Svg
      width={svgWidth}
      height={svgHeight}
      style={style}
      viewBox={`0 0 ${svgWidth} ${svgHeight}`}
      preserveAspectRatio="none"
    >
      <Path
        d={d}
        transform={transforms[side]}
        stroke={stroke}
        strokeWidth={strokeWidth}
        fill="none"
        strokeLinecap="round"
      />
    </Svg>
  );
};