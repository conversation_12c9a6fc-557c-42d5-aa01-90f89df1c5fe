如何打包已完成的 React Native Expo 项目为 APK
打包 React Native Expo 项目为 APK 有几种方法，下面我将详细介绍最常用的两种方式：使用 EAS Build（推荐）和传统的 Expo 构建方法。
方法 1：使用 EAS Build（推荐方式）
EAS (Expo Application Services) 是 Expo 官方推荐的新一代构建服务，它提供了更灵活、可靠的构建流程。
步骤 1：安装 EAS CLI
npm install -g eas-cli

步骤 2：登录 Expo 账户
eas login

如果你没有 Expo 账户，需要先在 https://expo.dev/signup 注册一个。
步骤 3：配置 EAS Build
eas build:configure

在项目根目录运行：
这会在项目中创建一个 eas.json 文件，该文件包含构建配置。
步骤 4：修改 eas.json 文件
对于 APK 构建，你可以添加或修改 eas.json 文件中的 build 部分：
{
  "build": {
    "preview": {
      "android": {
        "buildType": "apk"
      }
    },
    "production": {
      "android": {
        "buildType": "app-bundle"
      }
    }
  }
}

步骤 6：启动构建
对于 APK 构建，运行：
eas build -p android --profile preview

这将启动云构建过程。完成后，你可以从 Expo 网站下载 APK，或者命令行会提供下载链接。

方法 2：使用 Expo 开发构建和标准 React Native 工具
如果你希望在本地构建，或者需要更多自定义选项，可以使用这种方法。
步骤 1：安装必要的开发工具
确保你已安装 Android Studio 和相关 SDK。
步骤 2：弹出项目为开发构建
npx expo prebuild -p android

这将生成原生 Android 项目文件。
步骤 3：构建 APK
cd android
./gradlew assembleRelease

APK 文件将生成在 android/app/build/outputs/apk/release/app-release.apk。

步骤 4：签名 APK（必要）
对于发布到 Google Play 的应用，你需要签名 APK：
创建签名密钥：
keytool -genkey -v -keystore my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000

将密钥存储文件移动到 android/app 目录。
在 android/gradle.properties 中添加：
MYAPP_RELEASE_STORE_FILE=my-release-key.keystore
MYAPP_RELEASE_KEY_ALIAS=my-key-alias
MYAPP_RELEASE_STORE_PASSWORD=*****
MYAPP_RELEASE_KEY_PASSWORD=*****

在 android/app/build.gradle 中配置签名：
signingConfigs {
    release {
        storeFile file(MYAPP_RELEASE_STORE_FILE)
        storePassword MYAPP_RELEASE_STORE_PASSWORD
        keyAlias MYAPP_RELEASE_KEY_ALIAS
        keyPassword MYAPP_RELEASE_KEY_PASSWORD
    }
}
buildTypes {
    release {
        signingConfig signingConfigs.release
    }
}

重新构建：
./gradlew assembleRelease
