{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.0.0", "expo": "^53.0.0", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lunar-javascript": "^1.6.13", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.0.2", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^18.0.7", "jest": "^29.7.0", "jest-expo": "~53.0.5", "react-test-renderer": "19.0.0", "reactotron-react-native": "^5.1.12", "typescript": "~5.8.3"}, "private": true}